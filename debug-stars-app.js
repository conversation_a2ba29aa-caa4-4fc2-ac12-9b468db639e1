// 🌟 Script de Débogage - Étoiles TimeTracker V4
// À exécuter dans la console du navigateur quand l'app est ouverte

console.log('🌟 DÉMARRAGE DU DIAGNOSTIC DES ÉTOILES');
console.log('=====================================');

// 1. DIAGNOSTIC INITIAL - Vérifier la présence des étoiles
function diagnosticInitial() {
    console.log('\n🔍 DIAGNOSTIC INITIAL:');
    
    // Chercher le container des étoiles
    const starsContainer = document.querySelector('[style*="z-index: 7"]');
    console.log('Container étoiles trouvé:', starsContainer);
    
    if (starsContainer) {
        const stars = starsContainer.querySelectorAll('.fixed-star');
        console.log(`📊 Nombre d'étoiles dans le DOM: ${stars.length}`);
        
        if (stars.length > 0) {
            console.log('✅ Étoiles présentes dans le DOM');
            
            // Analyser les styles de quelques étoiles
            for (let i = 0; i < Math.min(5, stars.length); i++) {
                const star = stars[i];
                const styles = window.getComputedStyle(star);
                console.log(`⭐ Étoile ${i}:`, {
                    opacity: styles.opacity,
                    zIndex: styles.zIndex,
                    width: styles.width,
                    height: styles.height,
                    position: styles.position,
                    left: styles.left,
                    top: styles.top,
                    background: styles.background,
                    visibility: styles.visibility,
                    display: styles.display
                });
            }
        } else {
            console.log('❌ Aucune étoile trouvée dans le container');
        }
    } else {
        console.log('❌ Container des étoiles non trouvé');
    }
}

// 2. TEST DE VISIBILITÉ - Modifier temporairement les étoiles
function testVisibilite() {
    console.log('\n🔧 TEST DE VISIBILITÉ:');
    
    const stars = document.querySelectorAll('.fixed-star');
    console.log(`🎯 Modification de ${stars.length} étoiles pour test`);
    
    stars.forEach((star, index) => {
        // Sauvegarder les styles originaux
        if (!star.dataset.originalStyles) {
            star.dataset.originalStyles = JSON.stringify({
                zIndex: star.style.zIndex,
                opacity: star.style.opacity,
                width: star.style.width,
                height: star.style.height,
                background: star.style.background
            });
        }
        
        // Appliquer les modifications de test
        star.style.zIndex = '9999'; // Z-index très élevé
        star.style.opacity = '1'; // Opacité maximale
        star.style.width = '5px'; // Taille augmentée
        star.style.height = '5px'; // Taille augmentée
        star.style.background = 'rgba(255, 255, 0, 1)'; // Jaune vif pour visibilité
        star.style.boxShadow = '0 0 10px rgba(255, 255, 0, 0.8)'; // Halo jaune
    });
    
    console.log('✅ Modifications appliquées - Les étoiles devraient être visibles maintenant');
    console.log('💡 Si elles sont visibles → problème de z-index ou opacité');
    console.log('💡 Si elles ne sont pas visibles → problème plus profond');
}

// 3. RESTAURER LES STYLES ORIGINAUX
function restaurerStyles() {
    console.log('\n🔄 RESTAURATION DES STYLES:');
    
    const stars = document.querySelectorAll('.fixed-star');
    let restored = 0;
    
    stars.forEach(star => {
        if (star.dataset.originalStyles) {
            const original = JSON.parse(star.dataset.originalStyles);
            star.style.zIndex = original.zIndex;
            star.style.opacity = original.opacity;
            star.style.width = original.width;
            star.style.height = original.height;
            star.style.background = original.background;
            star.style.boxShadow = ''; // Retirer le halo de test
            delete star.dataset.originalStyles;
            restored++;
        }
    });
    
    console.log(`✅ ${restored} étoiles restaurées`);
}

// 4. CRÉER DES ÉTOILES DE TEST DIRECTEMENT
function creerEtoilesTest() {
    console.log('\n🌟 CRÉATION D\'ÉTOILES DE TEST:');
    
    // Trouver ou créer un container de test
    let testContainer = document.getElementById('test-stars-container');
    if (!testContainer) {
        testContainer = document.createElement('div');
        testContainer.id = 'test-stars-container';
        testContainer.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 9999;
        `;
        document.body.appendChild(testContainer);
    }
    
    // Créer 50 étoiles de test très visibles
    for (let i = 0; i < 50; i++) {
        const star = document.createElement('div');
        star.className = 'test-star';
        star.style.cssText = `
            position: absolute;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 70}%;
            width: 8px;
            height: 8px;
            background: rgba(255, 255, 0, 1);
            border-radius: 50%;
            box-shadow: 0 0 15px rgba(255, 255, 0, 0.8);
            z-index: 9999;
            animation: twinkle 2s infinite alternate;
        `;
        testContainer.appendChild(star);
    }
    
    // Ajouter l'animation CSS si elle n'existe pas
    if (!document.getElementById('test-stars-css')) {
        const style = document.createElement('style');
        style.id = 'test-stars-css';
        style.textContent = `
            @keyframes twinkle {
                0% { opacity: 0.3; transform: scale(0.8); }
                100% { opacity: 1; transform: scale(1.2); }
            }
        `;
        document.head.appendChild(style);
    }
    
    console.log('✅ 50 étoiles de test créées avec z-index 9999');
    console.log('💡 Si visibles → le système fonctionne, problème dans FixedStars');
}

// 5. NETTOYER LES ÉTOILES DE TEST
function nettoyerTest() {
    console.log('\n🧹 NETTOYAGE:');
    
    const testContainer = document.getElementById('test-stars-container');
    const testCSS = document.getElementById('test-stars-css');
    
    if (testContainer) {
        testContainer.remove();
        console.log('✅ Container de test supprimé');
    }
    
    if (testCSS) {
        testCSS.remove();
        console.log('✅ CSS de test supprimé');
    }
    
    // Restaurer les styles originaux aussi
    restaurerStyles();
}

// 6. DIAGNOSTIC COMPLET AUTOMATIQUE
function diagnosticComplet() {
    console.log('\n🚀 DIAGNOSTIC COMPLET AUTOMATIQUE:');
    console.log('==================================');
    
    diagnosticInitial();
    
    setTimeout(() => {
        testVisibilite();
        console.log('\n⏱️ Attendez 5 secondes pour observer...');
        
        setTimeout(() => {
            restaurerStyles();
            console.log('\n⏱️ Création d\'étoiles de test dans 2 secondes...');
            
            setTimeout(() => {
                creerEtoilesTest();
                console.log('\n⏱️ Nettoyage automatique dans 10 secondes...');
                
                setTimeout(() => {
                    nettoyerTest();
                    console.log('\n✅ DIAGNOSTIC TERMINÉ');
                    console.log('📋 Résumé des résultats dans la console ci-dessus');
                }, 10000);
            }, 2000);
        }, 5000);
    }, 1000);
}

// FONCTIONS DISPONIBLES DANS LA CONSOLE
window.debugStars = {
    diagnosticInitial,
    testVisibilite,
    restaurerStyles,
    creerEtoilesTest,
    nettoyerTest,
    diagnosticComplet
};

console.log('\n🎮 COMMANDES DISPONIBLES:');
console.log('debugStars.diagnosticInitial() - Vérifier la présence des étoiles');
console.log('debugStars.testVisibilite() - Rendre les étoiles très visibles');
console.log('debugStars.restaurerStyles() - Restaurer les styles originaux');
console.log('debugStars.creerEtoilesTest() - Créer des étoiles de test');
console.log('debugStars.nettoyerTest() - Nettoyer tous les tests');
console.log('debugStars.diagnosticComplet() - Lancer tous les tests automatiquement');
console.log('\n💡 Recommandé: debugStars.diagnosticComplet()');
