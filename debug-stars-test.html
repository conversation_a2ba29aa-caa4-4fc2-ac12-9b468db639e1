<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌟 Test de Débogage - Étoiles</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            position: relative;
            width: 100%;
            height: 500px;
            background: linear-gradient(to bottom, #001122, #000033);
            border: 2px solid #333;
            overflow: hidden;
        }
        
        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            pointer-events: none;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #333;
            color: white;
            border: 1px solid #666;
            cursor: pointer;
        }
        
        button:hover {
            background: #555;
        }
        
        .info {
            margin: 10px 0;
            padding: 10px;
            background: #222;
            border-left: 4px solid #0066cc;
        }
        
        .diagnostic {
            background: #330000;
            border-left: 4px solid #cc0000;
        }
        
        .success {
            background: #003300;
            border-left: 4px solid #00cc00;
        }
    </style>
</head>
<body>
    <h1>🌟 Test de Débogage - Étoiles TimeTracker V4</h1>
    
    <div class="info">
        <strong>OBJECTIF :</strong> Diagnostiquer pourquoi les étoiles ne sont pas visibles dans l'application principale
    </div>
    
    <div class="controls">
        <button onclick="testBasicStars()">1. Test Étoiles Basiques</button>
        <button onclick="testHighZIndex()">2. Test Z-Index Élevé (9999)</button>
        <button onclick="testLargeStars()">3. Test Étoiles Grosses</button>
        <button onclick="testManyStars()">4. Test Beaucoup d'Étoiles</button>
        <button onclick="clearStars()">Nettoyer</button>
    </div>
    
    <div class="test-container" id="testContainer">
        <!-- Les étoiles seront générées ici -->
    </div>
    
    <div id="diagnostics"></div>
    
    <script>
        const container = document.getElementById('testContainer');
        const diagnostics = document.getElementById('diagnostics');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `info ${type}`;
            div.innerHTML = `<strong>[${new Date().toLocaleTimeString()}]</strong> ${message}`;
            diagnostics.appendChild(div);
            console.log(message);
        }
        
        function clearStars() {
            container.innerHTML = '';
            diagnostics.innerHTML = '';
            log('🧹 Container nettoyé');
        }
        
        function createStar(x, y, size, zIndex = 7, opacity = 1) {
            const star = document.createElement('div');
            star.className = 'star';
            star.style.cssText = `
                left: ${x}%;
                top: ${y}%;
                width: ${size}px;
                height: ${size}px;
                z-index: ${zIndex};
                opacity: ${opacity};
                background: rgba(255, 255, 255, ${opacity});
                box-shadow: 0 0 ${size * 2}px rgba(255, 255, 255, ${opacity * 0.8});
            `;
            container.appendChild(star);
            return star;
        }
        
        function testBasicStars() {
            clearStars();
            log('🔍 TEST 1: Étoiles basiques (comme dans l\'app)', 'info');
            
            // Créer 50 étoiles avec les paramètres actuels de l'app
            for (let i = 0; i < 50; i++) {
                const x = Math.random() * 100;
                const y = Math.random() * 70; // Comme dans l'app
                const size = 0.5 + Math.random() * 2; // Tailles réalistes
                const opacity = 0.3 + Math.random() * 0.7;
                createStar(x, y, size, 7, opacity);
            }
            
            log(`✅ 50 étoiles créées avec z-index 7, tailles 0.5-2.5px`);
            
            // Diagnostic
            setTimeout(() => {
                const stars = container.querySelectorAll('.star');
                log(`📊 DIAGNOSTIC: ${stars.length} étoiles dans le DOM`);
                
                if (stars.length === 0) {
                    log('❌ PROBLÈME: Aucune étoile créée', 'diagnostic');
                } else if (stars.length < 50) {
                    log('⚠️ PROBLÈME: Certaines étoiles manquantes', 'diagnostic');
                } else {
                    log('✅ Toutes les étoiles sont présentes', 'success');
                }
            }, 100);
        }
        
        function testHighZIndex() {
            clearStars();
            log('🔍 TEST 2: Z-Index très élevé (9999)', 'info');
            
            // Créer 30 étoiles avec z-index très élevé
            for (let i = 0; i < 30; i++) {
                const x = Math.random() * 100;
                const y = Math.random() * 70;
                const size = 1 + Math.random() * 3;
                const opacity = 0.5 + Math.random() * 0.5;
                createStar(x, y, size, 9999, opacity);
            }
            
            log(`✅ 30 étoiles créées avec z-index 9999`);
            log(`🎯 Si visibles maintenant → problème de z-index dans l'app`);
        }
        
        function testLargeStars() {
            clearStars();
            log('🔍 TEST 3: Étoiles très grosses et lumineuses', 'info');
            
            // Créer 20 étoiles très visibles
            for (let i = 0; i < 20; i++) {
                const x = Math.random() * 100;
                const y = Math.random() * 70;
                const size = 5 + Math.random() * 10; // Très grosses
                const opacity = 0.8 + Math.random() * 0.2; // Très lumineuses
                createStar(x, y, size, 7, opacity);
            }
            
            log(`✅ 20 étoiles TRÈS GROSSES créées (5-15px)`);
            log(`🎯 Si visibles → problème de taille dans l'app`);
        }
        
        function testManyStars() {
            clearStars();
            log('🔍 TEST 4: Beaucoup d\'étoiles (comme mode nuit)', 'info');
            
            // Créer 1000 étoiles comme en mode nuit
            for (let i = 0; i < 1000; i++) {
                const x = Math.random() * 100;
                const y = Math.random() * 70;
                const size = 0.4 + Math.random() * 4;
                const opacity = 0.2 + Math.random() * 0.8;
                createStar(x, y, size, 7, opacity);
            }
            
            log(`✅ 1000 étoiles créées (simulation mode nuit)`);
            log(`🎯 Test de densité et performance`);
        }
        
        // Test automatique au chargement
        window.onload = function() {
            log('🚀 Page de test chargée - Prêt pour diagnostic', 'success');
            log('👆 Cliquez sur les boutons pour tester différents scénarios');
        };
    </script>
</body>
</html>
