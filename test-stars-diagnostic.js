// 🌟 Script de Test Rapide - Diagnostic Étoiles
// À copier-coller dans la console du navigateur

console.log('🌟 DÉMARRAGE TEST DIAGNOSTIC ÉTOILES');
console.log('===================================');

// Fonction pour passer en mode nuit et tester
function testStarsVisibility() {
    console.log('🌙 Passage en mode NUIT...');
    
    // Passer en mode nuit
    if (typeof night === 'function') {
        night();
    } else {
        console.log('⚠️ Fonction night() non disponible, essayez manuellement');
    }
    
    // Attendre 2 secondes puis analyser
    setTimeout(() => {
        console.log('\n🔍 ANALYSE DES ÉTOILES:');
        
        // Chercher les étoiles
        const stars = document.querySelectorAll('.fixed-star');
        console.log(`📊 Nombre d'étoiles trouvées: ${stars.length}`);
        
        if (stars.length === 0) {
            console.log('❌ PROBLÈME: Aucune étoile trouvée dans le DOM');
            return;
        }
        
        // Analyser les 10 premières étoiles
        console.log('\n📋 ANALYSE DES 10 PREMIÈRES ÉTOILES:');
        for (let i = 0; i < Math.min(10, stars.length); i++) {
            const star = stars[i];
            const styles = window.getComputedStyle(star);
            
            console.log(`⭐ Étoile ${i}:`, {
                visible: styles.opacity !== '0',
                opacity: styles.opacity,
                zIndex: styles.zIndex,
                size: `${styles.width} x ${styles.height}`,
                color: styles.backgroundColor,
                position: `${styles.left}, ${styles.top}`
            });
        }
        
        // Compter les étoiles visibles
        const visibleStars = Array.from(stars).filter(star => {
            const opacity = window.getComputedStyle(star).opacity;
            return parseFloat(opacity) > 0;
        });
        
        console.log(`\n✅ RÉSULTAT: ${visibleStars.length}/${stars.length} étoiles visibles`);
        
        if (visibleStars.length > 0) {
            console.log('🎉 SUCCÈS: Les étoiles sont visibles !');
            console.log('💡 Le problème était bien la taille/opacité/z-index');
        } else {
            console.log('❌ ÉCHEC: Les étoiles ne sont toujours pas visibles');
            console.log('💡 Le problème est plus profond dans l\'implémentation');
        }
        
    }, 2000);
}

// Fonction pour restaurer les valeurs normales (après test)
function restoreNormalStars() {
    console.log('🔄 RESTAURATION DES VALEURS NORMALES...');
    console.log('⚠️ Cette fonction nécessite de modifier le code source');
    console.log('📝 Voir les commentaires dans FixedStars.tsx marqués "TEST"');
}

// Lancer le test automatiquement
console.log('🚀 Lancement du test dans 1 seconde...');
setTimeout(testStarsVisibility, 1000);

// Rendre les fonctions disponibles globalement
window.testStarsVisibility = testStarsVisibility;
window.restoreNormalStars = restoreNormalStars;

console.log('\n🎮 COMMANDES DISPONIBLES:');
console.log('testStarsVisibility() - Tester la visibilité des étoiles');
console.log('restoreNormalStars() - Instructions pour restaurer les valeurs normales');
