// 🌙 Test du Nouveau Dégradé de Nuit
// À copier-coller dans la console du navigateur

console.log('🌙 TEST DU NOUVEAU DÉGRADÉ DE NUIT');
console.log('================================');

function testNightGradient() {
    console.log('🌙 Passage en mode NUIT pour tester le nouveau dégradé...');
    
    // Passer en mode nuit
    if (typeof night === 'function') {
        night();
        console.log('✅ Fonction night() appelée');
    } else {
        console.log('⚠️ Fonction night() non disponible, essayez manuellement');
        console.log('💡 Cliquez sur le bouton "Nuit" dans l\'interface');
    }
    
    // Analyser le dégradé après 2 secondes
    setTimeout(() => {
        console.log('\n🔍 ANALYSE DU DÉGRADÉ:');
        
        const gradientElement = document.querySelector('[style*="z-index: 0"]');
        if (gradientElement) {
            const styles = window.getComputedStyle(gradientElement);
            console.log('📊 Dégradé trouvé:', {
                backgroundImage: styles.backgroundImage,
                zIndex: styles.zIndex,
                position: styles.position
            });
            
            // Vérifier les nouvelles couleurs
            const bgImage = styles.backgroundImage;
            if (bgImage.includes('#2c3e50')) {
                console.log('✅ SUCCÈS: Nouvelles couleurs détectées !');
                console.log('🎨 Couleurs appliquées:');
                console.log('   - Haut (presque noir): #0d1117');
                console.log('   - Milieu (bleu très sombre): #1a252f');
                console.log('   - Bas (bleu moyen): #2c3e50');
            } else {
                console.log('⚠️ Anciennes couleurs encore présentes');
                console.log('💡 La transition peut prendre jusqu\'à 15 secondes');
            }
        } else {
            console.log('❌ Élément dégradé non trouvé');
        }
        
    }, 2000);
    
    // Vérification finale après 16 secondes (après transition complète)
    setTimeout(() => {
        console.log('\n🔍 VÉRIFICATION FINALE (après transition):');
        
        const gradientElement = document.querySelector('[style*="z-index: 0"]');
        if (gradientElement) {
            const styles = window.getComputedStyle(gradientElement);
            const bgImage = styles.backgroundImage;
            
            if (bgImage.includes('#2c3e50') || bgImage.includes('44, 62, 80')) {
                console.log('🎉 SUCCÈS COMPLET: Nouveau dégradé de nuit appliqué !');
                console.log('🌙 Effet obtenu: Haut très sombre → Bas bleu nocturne');
            } else {
                console.log('⚠️ Transition peut-être encore en cours...');
                console.log('🔄 Essayez de rappeler night() si nécessaire');
            }
        }
    }, 16000);
}

// Fonction pour comparer avec l'ancien dégradé
function compareGradients() {
    console.log('\n📊 COMPARAISON DES DÉGRADÉS:');
    console.log('ANCIEN (trop clair):');
    console.log('  - Haut: #0a0f1a (bleu-noir)');
    console.log('  - Milieu: #1e2a3a (bleu sombre)');
    console.log('  - Bas: #3a4a5c (bleu-gris)');
    console.log('');
    console.log('NOUVEAU (plus dramatique):');
    console.log('  - Haut: #0d1117 (presque noir)');
    console.log('  - Milieu: #1a252f (bleu très sombre)');
    console.log('  - Bas: #2c3e50 (bleu moyen nocturne)');
}

// Lancer le test automatiquement
console.log('🚀 Lancement du test dans 1 seconde...');
setTimeout(testNightGradient, 1000);

// Rendre les fonctions disponibles
window.testNightGradient = testNightGradient;
window.compareGradients = compareGradients;

console.log('\n🎮 COMMANDES DISPONIBLES:');
console.log('testNightGradient() - Tester le nouveau dégradé');
console.log('compareGradients() - Voir la comparaison ancien/nouveau');
